# 华为iDME模式下的业务实体模型生成

## 概述

基于华为iDME（工业数字模型驱动引擎）的元模型驱动理念，本文档展示如何生成销售订单、销售发票、会计凭证等业务实体模型。

## iDME核心理念

### 2类元模型
1. **实体元模型（EntityMetaModel）** - 描述业务实体的结构和属性
2. **关系元模型（RelationMetaModel）** - 描述实体间的关联关系

### 6类元关系
1. **继承关系（Inheritance）** - 表示类型的继承层次
2. **组合关系（Composition）** - 表示整体与部分的强关联
3. **聚合关系（Aggregation）** - 表示整体与部分的弱关联
4. **关联关系（Association）** - 表示实体间的一般性关联
5. **依赖关系（Dependency）** - 表示实体间的依赖关系
6. **实现关系（Realization）** - 表示接口与实现的关系

## 业务实体模型生成

### 1. 销售订单实体模型

```rust
// 生成销售订单实体
let mut generator = BusinessEntityGenerator::new();
let sales_order_id = generator.generate_sales_order_entity()?;
```

**销售订单属性：**
- `order_number`: 订单编号（必需）
- `order_date`: 订单日期（必需）
- `customer_id`: 客户ID（必需，引用Customer）
- `customer_name`: 客户名称（必需）
- `total_amount`: 订单总金额（必需）
- `currency`: 币种（必需）
- `status`: 订单状态（必需）
- `delivery_date`: 交货日期（可选）
- `sales_person`: 销售员（可选）
- `payment_terms`: 付款条件（可选）
- `notes`: 备注（可选）

### 2. 销售发票实体模型

```rust
// 生成销售发票实体
let invoice_id = generator.generate_sales_invoice_entity()?;
```

**销售发票属性：**
- `invoice_number`: 发票编号（必需）
- `invoice_date`: 发票日期（必需）
- `sales_order_id`: 销售订单ID（可选，引用SalesOrder）
- `customer_id`: 客户ID（必需，引用Customer）
- `customer_name`: 客户名称（必需）
- `subtotal_amount`: 小计金额（必需）
- `tax_amount`: 税额（必需）
- `total_amount`: 总金额（必需）
- `currency`: 币种（必需）
- `status`: 发票状态（必需）
- `due_date`: 到期日期（可选）
- `payment_status`: 付款状态（可选）

### 3. 会计凭证实体模型

```rust
// 生成会计凭证实体
let voucher_id = generator.generate_accounting_voucher_entity()?;
```

**会计凭证属性：**
- `voucher_number`: 凭证编号（必需）
- `voucher_date`: 凭证日期（必需）
- `voucher_type`: 凭证类型（必需）
- `reference_document_type`: 参考单据类型（可选）
- `reference_document_id`: 参考单据ID（可选）
- `total_debit_amount`: 借方总金额（必需）
- `total_credit_amount`: 贷方总金额（必需）
- `currency`: 币种（必需）
- `status`: 凭证状态（必需）
- `prepared_by`: 制单人（可选）
- `approved_by`: 审核人（可选）
- `description`: 摘要（可选）

### 4. 会计凭证明细实体模型

```rust
// 生成凭证明细实体
let voucher_line_id = generator.generate_voucher_line_entity()?;
```

**凭证明细属性：**
- `line_number`: 行号（必需）
- `account_code`: 科目代码（必需）
- `account_name`: 科目名称（必需）
- `debit_amount`: 借方金额（可选）
- `credit_amount`: 贷方金额（可选）
- `description`: 摘要（可选）
- `cost_center`: 成本中心（可选）
- `project_id`: 项目ID（可选，引用Project）

## 业务关系模型

### 1. 组合关系（Composition）
- **销售订单 → 订单明细**：一对多组合关系
- **会计凭证 → 凭证明细**：一对多组合关系

### 2. 关联关系（Association）
- **销售订单 → 客户**：多对一关联关系
- **销售发票 → 客户**：多对一关联关系

### 3. 依赖关系（Dependency）
- **销售发票 → 销售订单**：发票依赖于订单
- **会计凭证 → 销售发票**：凭证依赖于发票

## 完整业务流程模板生成

```rust
// 生成完整的销售业务流程模板
let template = generator.generate_sales_business_template()?;
```

**业务流程包含：**
1. **实体模型**：客户、销售订单、订单明细、销售发票、会计凭证、凭证明细
2. **关系模型**：6个关系模型连接各实体
3. **工作流步骤**：
   - 创建销售订单
   - 生成销售发票
   - 生成会计凭证

## 实例创建示例

### 1. 创建销售订单实例

```rust
let mut instance_manager = InstanceManager::new(generator.meta_repo.clone());

// 创建销售订单实例
let order_attrs = HashMap::from([
    ("order_number".to_string(), json!("SO-2024-001")),
    ("order_date".to_string(), json!(Utc::now().to_rfc3339())),
    ("customer_name".to_string(), json!("华为技术有限公司")),
    ("total_amount".to_string(), json!(100000)),
    ("currency".to_string(), json!("CNY")),
    ("status".to_string(), json!("已确认")),
]);

let order_instance_id = instance_manager.create_entity_instance(
    sales_order_meta_id,
    order_attrs,
)?;
```

### 2. 创建销售发票实例

```rust
let invoice_attrs = HashMap::from([
    ("invoice_number".to_string(), json!("INV-2024-001")),
    ("invoice_date".to_string(), json!(Utc::now().to_rfc3339())),
    ("customer_name".to_string(), json!("华为技术有限公司")),
    ("subtotal_amount".to_string(), json!(100000)),
    ("tax_amount".to_string(), json!(13000)),
    ("total_amount".to_string(), json!(113000)),
    ("currency".to_string(), json!("CNY")),
    ("status".to_string(), json!("已开票")),
]);

let invoice_instance_id = instance_manager.create_entity_instance(
    invoice_meta_id,
    invoice_attrs,
)?;
```

### 3. 创建会计凭证实例

```rust
let voucher_attrs = HashMap::from([
    ("voucher_number".to_string(), json!("V-2024-001")),
    ("voucher_date".to_string(), json!(Utc::now().to_rfc3339())),
    ("voucher_type".to_string(), json!("销售收入")),
    ("total_debit_amount".to_string(), json!(113000)),
    ("total_credit_amount".to_string(), json!(113000)),
    ("currency".to_string(), json!("CNY")),
    ("status".to_string(), json!("已过账")),
]);

let voucher_instance_id = instance_manager.create_entity_instance(
    voucher_meta_id,
    voucher_attrs,
)?;
```

## 数据图谱和追溯

### 业务流程追溯

```rust
// 构建业务流程图谱
let mut graph = DataGraph::new();

// 添加业务节点和边
graph.add_node(order_node);
graph.add_node(invoice_node);
graph.add_node(voucher_node);

graph.add_edge(order_invoice_edge);
graph.add_edge(invoice_voucher_edge);

// 执行追溯查询
let query_engine = GraphQueryEngine::new(graph);
let trace_path = query_engine.trace_path("order_001", "voucher_001", 10);
```

**追溯路径：** 销售订单 → 销售发票 → 会计凭证

## 优势特性

### 1. 元模型驱动
- **先建模，后实例**：确保数据结构的一致性
- **类型安全**：编译时检查数据类型
- **可扩展性**：易于添加新的业务实体和关系

### 2. 关系完整性
- **6类元关系**：覆盖所有业务关系场景
- **基数约束**：确保关系的正确性
- **导航性**：支持双向关系导航

### 3. 业务流程支持
- **工作流定义**：明确的业务流程步骤
- **状态管理**：跟踪业务单据状态
- **追溯能力**：支持业务流程的完整追溯

### 4. 数据图谱
- **自动入图**：业务数据自动构建图谱
- **分钟级追溯**：支持大规模数据的快速追溯
- **可视化**：业务关系的图形化展示

## 使用场景

1. **ERP系统**：销售管理、财务管理
2. **CRM系统**：客户关系管理
3. **财务系统**：会计核算、财务报表
4. **审计系统**：业务流程追溯、合规检查
5. **数据仓库**：业务数据建模、ETL过程

## 总结

基于华为iDME的元模型驱动理念，我们可以：

1. **标准化建模**：使用统一的元模型框架
2. **快速生成**：自动生成业务实体和关系模型
3. **完整追溯**：支持业务流程的端到端追溯
4. **灵活扩展**：易于适应业务变化和新需求

这种方法确保了业务系统的数据一致性、关系完整性和流程可追溯性，为企业数字化转型提供了坚实的数据基础。

use ospbase::data::dataset::idme_metamodel::*;
use ospbase::data::dataset::idme_metamodel::business_process_extensions::*;
use std::collections::HashMap;

/// 华为iDME业务实体模型生成演示
/// 
/// 本示例展示如何基于iDME元模型驱动理念生成：
/// - 销售订单实体模型
/// - 销售发票实体模型  
/// - 会计凭证实体模型
/// - 以及它们之间的关系模型

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== 华为iDME业务实体模型生成演示 ===\n");

    // 1. 创建业务实体生成器
    let mut generator = BusinessEntityGenerator::new();
    
    // 2. 生成完整的销售业务流程模板
    demo_generate_sales_business_template(&mut generator)?;
    
    // 3. 演示实体实例创建
    demo_create_business_instances(&generator)?;
    
    // 4. 演示业务流程追溯
    demo_business_process_tracing(&generator)?;

    println!("\n=== 演示完成 ===");
    Ok(())
}

/// 演示生成销售业务流程模板
fn demo_generate_sales_business_template(generator: &mut BusinessEntityGenerator) -> Result<(), Box<dyn std::error::Error>> {
    println!("1. 生成销售业务流程模板");
    
    // 生成完整的销售业务流程模板
    let template = generator.generate_sales_business_template()?;
    
    println!("   - 模板名称: {}", template.name);
    println!("   - 流程类型: {}", template.process_type);
    println!("   - 实体数量: {}", template.entity_ids.len());
    println!("   - 关系数量: {}", template.relation_ids.len());
    println!("   - 工作流步骤: {}", template.workflow_steps.len());
    
    // 显示生成的实体
    println!("\n   生成的实体模型:");
    for entity in generator.meta_repo.entities.values() {
        println!("     * {} ({})", entity.name, entity.namespace);
        println!("       - 属性数量: {}", entity.attributes.len());
        
        // 显示关键属性
        for (_, attr) in entity.attributes.iter().take(3) {
            let required = if attr.is_required { "必需" } else { "可选" };
            println!("         - {}: {:?} ({})", attr.name, attr.data_type, required);
        }
        if entity.attributes.len() > 3 {
            println!("         - ... 还有 {} 个属性", entity.attributes.len() - 3);
        }
    }
    
    // 显示生成的关系
    println!("\n   生成的关系模型:");
    for relation in generator.meta_repo.relations.values() {
        println!("     * {} ({:?})", relation.name, relation.relation_type);
        
        // 查找源实体和目标实体名称
        let source_name = generator.meta_repo.entities.get(&relation.source_entity)
            .map(|e| e.name.as_str()).unwrap_or("Unknown");
        let target_name = generator.meta_repo.entities.get(&relation.target_entity)
            .map(|e| e.name.as_str()).unwrap_or("Unknown");
        
        println!("       - {} -> {}", source_name, target_name);
        println!("       - 基数: {}:{} -> {}:{}", 
            relation.cardinality.source_min,
            relation.cardinality.source_max.map_or("*".to_string(), |n| n.to_string()),
            relation.cardinality.target_min,
            relation.cardinality.target_max.map_or("*".to_string(), |n| n.to_string())
        );
    }
    
    // 显示工作流步骤
    println!("\n   工作流步骤:");
    for step in &template.workflow_steps {
        println!("     * {}: {} ({})", step.step_id, step.step_name, step.entity_type);
        if !step.next_steps.is_empty() {
            println!("       - 下一步: {:?}", step.next_steps);
        }
    }
    
    println!("   ✓ 销售业务流程模板生成完成\n");
    Ok(())
}

/// 演示创建业务实例
fn demo_create_business_instances(generator: &BusinessEntityGenerator) -> Result<(), Box<dyn std::error::Error>> {
    println!("2. 创建业务实例演示");
    
    let mut instance_manager = InstanceManager::new(generator.meta_repo.clone());
    
    // 创建客户实例
    if let Some(customer_meta) = generator.meta_repo.entities.values().find(|e| e.name == "Customer") {
        let customer_attrs = create_customer_attributes(customer_meta)?;
        let customer_instance_id = instance_manager.create_entity_instance(
            customer_meta.id.clone(),
            customer_attrs,
        )?;
        println!("   - 创建客户实例: {}", customer_instance_id);
    }
    
    // 创建销售订单实例
    if let Some(order_meta) = generator.meta_repo.entities.values().find(|e| e.name == "SalesOrder") {
        let order_attrs = create_sales_order_attributes(order_meta)?;
        let order_instance_id = instance_manager.create_entity_instance(
            order_meta.id.clone(),
            order_attrs,
        )?;
        println!("   - 创建销售订单实例: {}", order_instance_id);
        
        // 创建订单明细实例
        if let Some(line_meta) = generator.meta_repo.entities.values().find(|e| e.name == "SalesOrderLine") {
            let line_attrs = create_order_line_attributes(line_meta)?;
            let line_instance_id = instance_manager.create_entity_instance(
                line_meta.id.clone(),
                line_attrs,
            )?;
            println!("   - 创建订单明细实例: {}", line_instance_id);
        }
    }
    
    // 创建销售发票实例
    if let Some(invoice_meta) = generator.meta_repo.entities.values().find(|e| e.name == "SalesInvoice") {
        let invoice_attrs = create_invoice_attributes(invoice_meta)?;
        let invoice_instance_id = instance_manager.create_entity_instance(
            invoice_meta.id.clone(),
            invoice_attrs,
        )?;
        println!("   - 创建销售发票实例: {}", invoice_instance_id);
    }
    
    // 创建会计凭证实例
    if let Some(voucher_meta) = generator.meta_repo.entities.values().find(|e| e.name == "AccountingVoucher") {
        let voucher_attrs = create_voucher_attributes(voucher_meta)?;
        let voucher_instance_id = instance_manager.create_entity_instance(
            voucher_meta.id.clone(),
            voucher_attrs,
        )?;
        println!("   - 创建会计凭证实例: {}", voucher_instance_id);
        
        // 创建凭证明细实例
        if let Some(voucher_line_meta) = generator.meta_repo.entities.values().find(|e| e.name == "VoucherLine") {
            // 借方明细
            let debit_line_attrs = create_voucher_line_attributes(voucher_line_meta, true)?;
            let debit_line_id = instance_manager.create_entity_instance(
                voucher_line_meta.id.clone(),
                debit_line_attrs,
            )?;
            println!("   - 创建凭证借方明细: {}", debit_line_id);
            
            // 贷方明细
            let credit_line_attrs = create_voucher_line_attributes(voucher_line_meta, false)?;
            let credit_line_id = instance_manager.create_entity_instance(
                voucher_line_meta.id.clone(),
                credit_line_attrs,
            )?;
            println!("   - 创建凭证贷方明细: {}", credit_line_id);
        }
    }
    
    println!("   - 总实例数: {}", instance_manager.entities.len());
    println!("   ✓ 业务实例创建完成\n");
    Ok(())
}

/// 演示业务流程追溯
fn demo_business_process_tracing(generator: &BusinessEntityGenerator) -> Result<(), Box<dyn std::error::Error>> {
    println!("3. 业务流程追溯演示");
    
    // 构建业务流程图谱
    let mut graph = data_graph::DataGraph::new();
    
    // 模拟业务流程节点
    let order_node = data_graph::DataNode {
        id: "order_001".to_string(),
        entity_instance_id: "sales_order_001".to_string(),
        node_type: "SalesOrder".to_string(),
        properties: {
            let mut props = HashMap::new();
            props.insert("order_number".to_string(), 
                serde_json::Value::String("SO-2024-001".to_string()));
            props.insert("customer_name".to_string(), 
                serde_json::Value::String("华为技术有限公司".to_string()));
            props.insert("total_amount".to_string(), 
                serde_json::Value::Number(serde_json::Number::from(100000)));
            props
        },
        created_at: chrono::Utc::now(),
    };
    
    let invoice_node = data_graph::DataNode {
        id: "invoice_001".to_string(),
        entity_instance_id: "sales_invoice_001".to_string(),
        node_type: "SalesInvoice".to_string(),
        properties: {
            let mut props = HashMap::new();
            props.insert("invoice_number".to_string(), 
                serde_json::Value::String("INV-2024-001".to_string()));
            props.insert("total_amount".to_string(), 
                serde_json::Value::Number(serde_json::Number::from(113000)));
            props
        },
        created_at: chrono::Utc::now(),
    };
    
    let voucher_node = data_graph::DataNode {
        id: "voucher_001".to_string(),
        entity_instance_id: "accounting_voucher_001".to_string(),
        node_type: "AccountingVoucher".to_string(),
        properties: {
            let mut props = HashMap::new();
            props.insert("voucher_number".to_string(), 
                serde_json::Value::String("V-2024-001".to_string()));
            props.insert("total_debit_amount".to_string(), 
                serde_json::Value::Number(serde_json::Number::from(113000)));
            props
        },
        created_at: chrono::Utc::now(),
    };
    
    graph.add_node(order_node);
    graph.add_node(invoice_node);
    graph.add_node(voucher_node);
    
    // 添加业务流程边
    let order_invoice_edge = data_graph::DataEdge {
        id: "edge_order_invoice".to_string(),
        source_node_id: "order_001".to_string(),
        target_node_id: "invoice_001".to_string(),
        relation_instance_id: "order_to_invoice_001".to_string(),
        edge_type: "generates".to_string(),
        properties: HashMap::new(),
        created_at: chrono::Utc::now(),
    };
    
    let invoice_voucher_edge = data_graph::DataEdge {
        id: "edge_invoice_voucher".to_string(),
        source_node_id: "invoice_001".to_string(),
        target_node_id: "voucher_001".to_string(),
        relation_instance_id: "invoice_to_voucher_001".to_string(),
        edge_type: "creates".to_string(),
        properties: HashMap::new(),
        created_at: chrono::Utc::now(),
    };
    
    graph.add_edge(order_invoice_edge);
    graph.add_edge(invoice_voucher_edge);
    
    println!("   - 业务流程图谱节点数: {}", graph.nodes.len());
    println!("   - 业务流程图谱边数: {}", graph.edges.len());
    
    // 演示从销售订单到会计凭证的追溯
    let query_engine = data_graph::GraphQueryEngine::new(graph);
    
    if let Some(trace_path) = query_engine.trace_path("order_001", "voucher_001", 10) {
        println!("   - 业务流程追溯成功:");
        println!("     * 起点: 销售订单 ({})", trace_path.source_node_id);
        println!("     * 终点: 会计凭证 ({})", trace_path.target_node_id);
        println!("     * 路径长度: {}", trace_path.path_length);
        println!("     * 流程路径: 销售订单 -> 销售发票 -> 会计凭证");
        
        // 显示路径详情
        for (i, node_id) in trace_path.nodes.iter().enumerate() {
            if let Some(node) = query_engine.graph.nodes.get(node_id) {
                if let Some(doc_number) = node.properties.get("order_number")
                    .or_else(|| node.properties.get("invoice_number"))
                    .or_else(|| node.properties.get("voucher_number")) {
                    println!("       {}. {} - {}", i + 1, node.node_type, doc_number);
                }
            }
        }
    } else {
        println!("   - 未找到业务流程追溯路径");
    }
    
    println!("   ✓ 业务流程追溯演示完成\n");
    Ok(())
}

// 辅助函数：创建各种实体的属性
fn create_customer_attributes(meta: &EntityMetaModel) -> Result<HashMap<String, serde_json::Value>, Box<dyn std::error::Error>> {
    let mut attrs = HashMap::new();
    
    for (attr_id, attr_def) in &meta.attributes {
        match attr_def.name.as_str() {
            "customer_code" => attrs.insert(attr_id.clone(), serde_json::Value::String("CUST001".to_string())),
            "customer_name" => attrs.insert(attr_id.clone(), serde_json::Value::String("华为技术有限公司".to_string())),
            "customer_type" => attrs.insert(attr_id.clone(), serde_json::Value::String("企业客户".to_string())),
            "contact_person" => attrs.insert(attr_id.clone(), serde_json::Value::String("张经理".to_string())),
            "phone" => attrs.insert(attr_id.clone(), serde_json::Value::String("13800138000".to_string())),
            "email" => attrs.insert(attr_id.clone(), serde_json::Value::String("<EMAIL>".to_string())),
            _ => None,
        };
    }
    
    Ok(attrs)
}

fn create_sales_order_attributes(meta: &EntityMetaModel) -> Result<HashMap<String, serde_json::Value>, Box<dyn std::error::Error>> {
    let mut attrs = HashMap::new();
    
    for (attr_id, attr_def) in &meta.attributes {
        if attr_def.is_required {
            match attr_def.name.as_str() {
                "order_number" => attrs.insert(attr_id.clone(), serde_json::Value::String("SO-2024-001".to_string())),
                "order_date" => attrs.insert(attr_id.clone(), serde_json::Value::String(chrono::Utc::now().to_rfc3339())),
                "customer_name" => attrs.insert(attr_id.clone(), serde_json::Value::String("华为技术有限公司".to_string())),
                "total_amount" => attrs.insert(attr_id.clone(), serde_json::Value::Number(serde_json::Number::from(100000))),
                "currency" => attrs.insert(attr_id.clone(), serde_json::Value::String("CNY".to_string())),
                "status" => attrs.insert(attr_id.clone(), serde_json::Value::String("已确认".to_string())),
                _ => None,
            };
        }
    }
    
    Ok(attrs)
}

fn create_order_line_attributes(meta: &EntityMetaModel) -> Result<HashMap<String, serde_json::Value>, Box<dyn std::error::Error>> {
    let mut attrs = HashMap::new();
    
    for (attr_id, attr_def) in &meta.attributes {
        if attr_def.is_required {
            match attr_def.name.as_str() {
                "line_number" => attrs.insert(attr_id.clone(), serde_json::Value::Number(serde_json::Number::from(1))),
                "product_name" => attrs.insert(attr_id.clone(), serde_json::Value::String("华为Mate60 Pro".to_string())),
                "quantity" => attrs.insert(attr_id.clone(), serde_json::Value::Number(serde_json::Number::from(10))),
                "unit_price" => attrs.insert(attr_id.clone(), serde_json::Value::Number(serde_json::Number::from(10000))),
                "line_amount" => attrs.insert(attr_id.clone(), serde_json::Value::Number(serde_json::Number::from(100000))),
                _ => None,
            };
        }
    }
    
    Ok(attrs)
}

fn create_invoice_attributes(meta: &EntityMetaModel) -> Result<HashMap<String, serde_json::Value>, Box<dyn std::error::Error>> {
    let mut attrs = HashMap::new();
    
    for (attr_id, attr_def) in &meta.attributes {
        if attr_def.is_required {
            match attr_def.name.as_str() {
                "invoice_number" => attrs.insert(attr_id.clone(), serde_json::Value::String("INV-2024-001".to_string())),
                "invoice_date" => attrs.insert(attr_id.clone(), serde_json::Value::String(chrono::Utc::now().to_rfc3339())),
                "customer_name" => attrs.insert(attr_id.clone(), serde_json::Value::String("华为技术有限公司".to_string())),
                "subtotal_amount" => attrs.insert(attr_id.clone(), serde_json::Value::Number(serde_json::Number::from(100000))),
                "tax_amount" => attrs.insert(attr_id.clone(), serde_json::Value::Number(serde_json::Number::from(13000))),
                "total_amount" => attrs.insert(attr_id.clone(), serde_json::Value::Number(serde_json::Number::from(113000))),
                "currency" => attrs.insert(attr_id.clone(), serde_json::Value::String("CNY".to_string())),
                "status" => attrs.insert(attr_id.clone(), serde_json::Value::String("已开票".to_string())),
                _ => None,
            };
        }
    }
    
    Ok(attrs)
}

fn create_voucher_attributes(meta: &EntityMetaModel) -> Result<HashMap<String, serde_json::Value>, Box<dyn std::error::Error>> {
    let mut attrs = HashMap::new();
    
    for (attr_id, attr_def) in &meta.attributes {
        if attr_def.is_required {
            match attr_def.name.as_str() {
                "voucher_number" => attrs.insert(attr_id.clone(), serde_json::Value::String("V-2024-001".to_string())),
                "voucher_date" => attrs.insert(attr_id.clone(), serde_json::Value::String(chrono::Utc::now().to_rfc3339())),
                "voucher_type" => attrs.insert(attr_id.clone(), serde_json::Value::String("销售收入".to_string())),
                "total_debit_amount" => attrs.insert(attr_id.clone(), serde_json::Value::Number(serde_json::Number::from(113000))),
                "total_credit_amount" => attrs.insert(attr_id.clone(), serde_json::Value::Number(serde_json::Number::from(113000))),
                "currency" => attrs.insert(attr_id.clone(), serde_json::Value::String("CNY".to_string())),
                "status" => attrs.insert(attr_id.clone(), serde_json::Value::String("已过账".to_string())),
                _ => None,
            };
        }
    }
    
    Ok(attrs)
}

fn create_voucher_line_attributes(meta: &EntityMetaModel, is_debit: bool) -> Result<HashMap<String, serde_json::Value>, Box<dyn std::error::Error>> {
    let mut attrs = HashMap::new();
    
    for (attr_id, attr_def) in &meta.attributes {
        match attr_def.name.as_str() {
            "line_number" => attrs.insert(attr_id.clone(), serde_json::Value::Number(serde_json::Number::from(if is_debit { 1 } else { 2 }))),
            "account_code" => attrs.insert(attr_id.clone(), serde_json::Value::String(if is_debit { "1122" } else { "6001" }.to_string())),
            "account_name" => attrs.insert(attr_id.clone(), serde_json::Value::String(if is_debit { "应收账款" } else { "主营业务收入" }.to_string())),
            "debit_amount" => {
                if is_debit {
                    attrs.insert(attr_id.clone(), serde_json::Value::Number(serde_json::Number::from(113000)))
                } else {
                    None
                }
            },
            "credit_amount" => {
                if !is_debit {
                    attrs.insert(attr_id.clone(), serde_json::Value::Number(serde_json::Number::from(113000)))
                } else {
                    None
                }
            },
            "description" => attrs.insert(attr_id.clone(), serde_json::Value::String("销售华为Mate60 Pro".to_string())),
            _ => None,
        };
    }
    
    Ok(attrs)
}

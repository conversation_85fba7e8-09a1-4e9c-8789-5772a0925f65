{"rustc": 12610991425282158916, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17669703692130904899, "path": 18241396018646006313, "deps": [[5103565458935487, "futures_io", false, 14270442065038919243], [1615478164327904835, "pin_utils", false, 12469460373921022268], [1811549171721445101, "futures_channel", false, 8358389760768656412], [2877347214279964928, "pin_project_lite", false, 16950939044251571328], [3129130049864710036, "memchr", false, 13362257843400240769], [6955678925937229351, "slab", false, 10696779347599728358], [7013762810557009322, "futures_sink", false, 18011676830810919344], [7620660491849607393, "futures_core", false, 1933269606143815352], [10565019901765856648, "futures_macro", false, 247682254143120296], [16240732885093539806, "futures_task", false, 219849869341807092]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/futures-util-aece7f8e116ff40d/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
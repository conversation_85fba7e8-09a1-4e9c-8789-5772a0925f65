{"rustc": 12610991425282158916, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"macos-system-configuration\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 2383083043656166682, "path": 6513093221406148033, "deps": [[40386456601120721, "percent_encoding", false, 14251064355375274525], [784494742817713399, "tower_service", false, 2965515054055075290], [985115344064483054, "system_configuration", false, 8086178974594352384], [2517136641825875337, "sync_wrapper", false, 13839528147585270157], [2877347214279964928, "pin_project_lite", false, 16950939044251571328], [3107892209185275978, "tokio", false, 4727567445113533239], [3150220818285335163, "url", false, 2049043707044127943], [4920660634395069245, "hyper_util", false, 12209022179351510194], [5070769681332304831, "once_cell", false, 12773165180629761908], [6572616762007422061, "h2", false, 8589159992508639064], [7314894124883917868, "log", false, 11931379525547706031], [7620660491849607393, "futures_core", false, 1933269606143815352], [9648403166091088614, "native_tls_crate", false, 5355314834874158037], [10036721834787556336, "http_body_util", false, 17654821198667782404], [10229185211513642314, "mime", false, 2670213595488860123], [10629569228670356391, "futures_util", false, 9265724029509349223], [10967960060725374459, "serde", false, 5680163056059102474], [11723101755950938843, "ipnet", false, 14397935522079671980], [12186126227181294540, "tokio_native_tls", false, 18127964196330627378], [13077212702700853852, "base64", false, 11590762642269468431], [14084095096285906100, "http_body", false, 11447607033176071398], [14564311161534545801, "encoding_rs", false, 17617067273727869891], [15032952994102373905, "rustls_pemfile", false, 6737230997308878878], [16542808166767769916, "serde_urlencoded", false, 15591432060963353130], [17382435053614925174, "bytes", false, 2688969658661843922], [17860019243264344128, "http", false, 8314166824194695662], [17936921378057108349, "hyper", false, 10769914926790831700], [18273243456331255970, "hyper_tls", false, 9376623788102704523]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-cf9fdd03f00154be/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}
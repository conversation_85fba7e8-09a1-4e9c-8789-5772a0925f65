{"rustc": 12610991425282158916, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-targets\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-targets\"]", "target": 15315924755136109342, "profile": 5347358027863023418, "path": 3912826076466844422, "deps": [[2133346516990077175, "iana_time_zone", false, 8304171083815012951], [5157631553186200874, "num_traits", false, 4718211857357733075], [10967960060725374459, "serde", false, 5680163056059102474]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/chrono-42001879241ed1dc/dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}